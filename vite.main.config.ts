import { defineConfig } from 'vite';
import { builtinModules } from 'node:module';
import { copyFileSync, mkdirSync } from 'node:fs';
import { join, dirname } from 'node:path';

// https://vitejs.dev/config
export default defineConfig({
  build: {
    rollupOptions: {
      external: [
        'electron',
        'better-sqlite3',
        ...builtinModules.flatMap((p) => [p, `node:${p}`]),
      ],
      output: {
        format: 'cjs',
        entryFileNames: '[name].cjs',
      },
    },
  },
  resolve: {
    // Ensure Node.js conditions for main process build
    conditions: ['node'],
    mainFields: ['module', 'jsnext:main', 'jsnext'],
  },
  plugins: [
    {
      name: 'copy-schema',
      writeBundle() {
        // Copy schema.sql to build directory
        const srcPath = join(__dirname, 'src', 'database', 'schema.sql');
        const destPath = join(__dirname, '.vite', 'build', 'schema.sql');

        try {
          mkdirSync(dirname(destPath), { recursive: true });
          copyFileSync(srcPath, destPath);
          console.log('Schema file copied to build directory');
        } catch (error) {
          console.error('Failed to copy schema file:', error);
        }
      },
    },
  ],
});
