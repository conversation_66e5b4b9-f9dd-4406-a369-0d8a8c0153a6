import React, { useCallback, useEffect, useState } from 'react';
import { useAppStore } from './store';
import { useAgentLoop } from './hooks/useAgentLoop';
import { useKeyboardShortcuts, useClipboard, useGlobalKeyboardEvents } from './hooks/useKeyboardShortcuts';
import { MainInterface } from './components/MainInterface';
import { PromptInput } from './components/PromptInput';
import { FooterStatusBar } from './components/FooterStatusBar';
import { SettingsModal } from './components/settings/SettingsModal';
import { ConfirmationModal } from './components/modals/ConfirmationModal';

const App: React.FC = () => {
  const config = useAppStore((state) => state.config);
  const updateConfig = useAppStore((state) => state.updateConfig);
  const isSettingsOpen = useAppStore((state) => state.isSettingsOpen);
  const setSettingsOpen = useAppStore((state) => state.setSettingsOpen);
  const isConfirmationModalOpen = useAppStore((state) => state.isConfirmationModalOpen);
  const setConfirmationModalOpen = useAppStore((state) => state.setConfirmationModalOpen);

  // Use the agent loop hook for actual message processing
  const {
    agentState,
    messages,
    currentPlan,
    error,
    canSendMessage,
    lastAIMessage,
    processPrompt,
    confirmPlan,
    cancelPlan,
    clearMessages: clearAgentMessages
  } = useAgentLoop({ config });

  const { copyToClipboard } = useClipboard();

  // Set up global keyboard events
  useGlobalKeyboardEvents();

  // Handle keyboard shortcuts
  const handleNewConversation = useCallback(() => {
    clearAgentMessages();
  }, [clearAgentMessages]);

  const handleToggleExecutionMode = useCallback(() => {
    const newMode = config.executionMode === 'confirm' ? 'yolo' : 'confirm';
    updateConfig({ executionMode: newMode });
  }, [config.executionMode, updateConfig]);

  const handleFocusInput = useCallback(() => {
    // Focus the input textarea
    const textarea = document.querySelector('textarea[placeholder="Type your message..."]') as HTMLTextAreaElement;
    if (textarea) {
      textarea.focus();
    }
  }, []);

  const handleCopyLastResponse = useCallback(() => {
    if (lastAIMessage) {
      copyToClipboard(lastAIMessage.content);
    }
  }, [lastAIMessage, copyToClipboard]);

  const handlePromptSubmit = useCallback(async (prompt: string) => {
    try {
      await processPrompt(prompt);
    } catch (error) {
      console.error('Error processing prompt:', error);
      // Error is handled by the useAgentLoop hook
    }
  }, [processPrompt]);

  useKeyboardShortcuts({
    onNewConversation: handleNewConversation,
    onSettings: () => setSettingsOpen(true),
    onToggleExecutionMode: handleToggleExecutionMode,
    onClearMessages: handleNewConversation,
    onFocusInput: handleFocusInput,
    onCopyLastResponse: handleCopyLastResponse,
    enabled: !isSettingsOpen && !isConfirmationModalOpen
  });

  const handleConfirmPlan = useCallback(() => {
    if (currentPlan) {
      confirmPlan(currentPlan);
      setConfirmationModalOpen(false);
    }
  }, [currentPlan, confirmPlan, setConfirmationModalOpen]);

  const handleCancelPlan = useCallback(() => {
    if (currentPlan) {
      cancelPlan();
      setConfirmationModalOpen(false);
    }
  }, [currentPlan, cancelPlan, setConfirmationModalOpen]);

  // Show confirmation modal when plan requires confirmation
  useEffect(() => {
    if (currentPlan && currentPlan.requiresConfirmation && config.executionMode === 'confirm') {
      setConfirmationModalOpen(true);
    }
  }, [currentPlan, config.executionMode, setConfirmationModalOpen]);

  // Auto-focus input on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      handleFocusInput();
    }, 100);
    return () => clearTimeout(timer);
  }, [handleFocusInput]);

  return (
    <div className={`h-screen bg-[#0a0a0a] text-[#f0f0f0] flex flex-col overflow-hidden ${
      config.executionMode === 'yolo' ? 'border-2 border-red-500' : ''
    }`}>
      <div className="flex-1 overflow-hidden">
        <MainInterface
          messages={messages}
          error={error}
          agentState={agentState}
          config={config}
          onOpenSettings={() => setSettingsOpen(true)}
        />
      </div>

      <div className="flex-shrink-0">
        <PromptInput
          onSubmit={handlePromptSubmit}
          disabled={!canSendMessage}
        />
      </div>

      <FooterStatusBar
        agentState={agentState}
        config={config}
        onSettingsClick={() => setSettingsOpen(true)}
      />

      {/* Settings Modal */}
      <SettingsModal
        isOpen={isSettingsOpen}
        onClose={() => setSettingsOpen(false)}
        title="Settings"
        config={config}
        onSave={(newConfig) => {
          updateConfig(newConfig);
          setSettingsOpen(false);
        }}
      />

      {/* Confirmation Modal */}
      {currentPlan && (
        <ConfirmationModal
          isOpen={isConfirmationModalOpen}
          onClose={() => setConfirmationModalOpen(false)}
          title="Confirm Plan Execution"
          plan={currentPlan}
          onConfirm={handleConfirmPlan}
          onCancel={handleCancelPlan}
        />
      )}
    </div>
  );
};

export default App;
