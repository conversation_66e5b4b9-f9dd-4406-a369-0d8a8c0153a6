import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import wasm from 'vite-plugin-wasm';
import topLevelAwait from 'vite-plugin-top-level-await';
import { builtinModules } from 'module';

// https://vitejs.dev/config
export default defineConfig({
  plugins: [
    react(),
    wasm(),
    topLevelAwait(),
  ],
  esbuild: {
    jsx: 'automatic',
  },
  css: {
    postcss: './postcss.config.cjs',
  },
  build: {
    rollupOptions: {
      external: [
        'electron',
        'fs',
        'fs/promises',
        'path',
        'child_process',
        'util',
        'better-sqlite3',
        ...builtinModules.flatMap((p) => [p, `node:${p}`]),
      ],
    },
  },
});
