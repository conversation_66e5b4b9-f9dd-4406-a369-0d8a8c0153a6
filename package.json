{"name": "arien-agent", "productName": "Arien Agent", "version": "1.0.0", "description": "A minimal Electron desktop application", "type": "module", "main": ".vite/build/main.cjs", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "ajay9", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.9.0", "@electron-forge/maker-deb": "^7.9.0", "@electron-forge/maker-rpm": "^7.9.0", "@electron-forge/maker-squirrel": "^7.9.0", "@electron-forge/maker-zip": "^7.9.0", "@electron-forge/plugin-auto-unpack-natives": "^7.9.0", "@electron-forge/plugin-fuses": "^7.9.0", "@electron-forge/plugin-vite": "^7.9.0", "@electron/fuses": "^1.8.0", "@types/electron-squirrel-startup": "^1.0.2", "@types/jest": "^30.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^5.0.3", "electron": "38.1.2", "eslint": "^8.57.1", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "jest": "^30.1.3", "ts-jest": "^29.4.4", "typescript": "^5.9.2", "vite": "^5.4.20"}, "dependencies": {"@anthropic-ai/sdk": "^0.63.0", "@tailwindcss/typography": "^0.5.18", "@types/better-sqlite3": "^7.6.13", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "axios": "^1.12.2", "better-sqlite3": "^12.4.1", "diff": "^8.0.2", "diff2html": "^3.4.52", "electron-squirrel-startup": "^1.0.1", "highlight.js": "^11.11.1", "immer": "^10.1.3", "openai": "^5.22.1", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hotkeys-hook": "^5.1.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwindcss": "^3.4.17", "tiktoken": "^1.0.22", "uuid": "^13.0.0", "vite-plugin-top-level-await": "^1.6.0", "vite-plugin-wasm": "^3.5.0", "zustand": "^5.0.8"}}